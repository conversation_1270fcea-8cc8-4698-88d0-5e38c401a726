name: diogen<PERSON><PERSON><PERSON>bot
description: Flutter project to create a chat bot using generative AI
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.920+920

environment:
  # sdk: "3.6.0-326.0.dev"
  sdk: "3.8.1"

#isar_version: 1.0.805+805

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter_localizations:
    sdk: flutter
  flutter:
    sdk: flutter

  convenient_test:
  mind_map:
  graphview:
  # mediapipe_core:
  # mediapipe_genai:
  # mediapipe_text:
  # flutter_mediapipe:
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: #^1.0.2
  english_words: #^4.0.0
  provider:
  # http: "0.13.0-nullsafety.0"
  http: ^1.2.2
  latlong2:
  universal_html: #^2.0.8
  flutter_dotenv: #^5.0.2
  firebase_core: ^3.9.0 #^3.3.0
  firebase_pagination:
  firebase_analytics:
  shared_preferences:
  flutter_markdown: #^0.6.14
  # tex_markdown:
  # flutter_markdown_latex:
  sqflite: #^2.2.5
  path:
  path_provider:
  sticky_headers:
  sqlite3_flutter_libs:
  sqlite3:
  equatable: #^2.0.5
  clipboard:
  flutter_map:
  dropdown_search:
  google_static_maps_controller:
  maps_launcher:
  # openapi_generator_annotations: ^1.1.4
  # swagger
  json_annotation: #^4.4.0
  expandable_text: #^2.3.0
  # eventsource3: #^0.4.0
  sse:
  # firebase_database:
  #    share:
  markdown_widget: #^2.1.0
  #firebase_dynamic_links:
  url_strategy:
  flutter_bloc: #^8.1.2
  firebase_storage:
  # wechat_assets_picker: #^8.4.0
  image_picker: #^0.8.7
  cached_network_image: #^3.2.3
  permission_handler: ^11.4.0
  # wechat_camera_picker: #^3.7.0
  #  chewie: ^1.8.5
  flutter_cache_manager: #^3.3.0
  firebase_performance: #^0.9.1+1
  firebase_app_check: ^0.3.1+7
  #    flutter_sound: #^9.2.13
  #    flutter_sound_record:
  #    audio_session: #^0.1.13
  share_plus: ^10.1.4
  photo_view: #^0.14.0
  # photo_manager:
  video_editor:
  #  video_trimmer: ^4.0.2
  flutter_link_previewer: #^3.2.0
  url_launcher: #^6.1.10
  any_link_preview: #^3.0.0
  image: #^4.0.17
  file_picker: #^5.2.6
  #    epub_viewer: ^0.2.5
  # internet_file: #^1.2.0
  pdfx: #^2.8.0
  # syncfusion_flutter_pdf:
  # syncfusion_flutter_pdfviewer:
  # syncfusion_flutter_charts: #^27.1.52
  # syncfusion_flutter_datepicker: #^27.1.52
  # syncfusion_flutter_calendar: #^27.1.52
  # syncfusion_flutter_gauges: #^27.1.52
  # syncfusion_flutter_sliders: #^27.1.52
  # syncfusion_flutter_datagrid: #^27.1.52
  expandable_page_view:
  dart_ping_ios: ^4.0.2
  dart_ping: ^9.0.1
  flutter_background:
  yaml: #^3.1.1
  firebase_messaging:
  dropdown_button2:
  #    json_serializable: #^6.5.4
  uuid: #^3.0.7
  geolocator: #^9.0.2
  google_fonts: #^4.0.3
  bloc: #^8.1.1
  googleapis: #^10.1.0
  flutter_mobx: #^2.0.6+5
  win32: #^3.1.3
  flutter_redux: #^0.10.0
  drift: #^2.6.0
  cloud_functions: #^4.1.0
  location: #^5.0.0-dev.8
  pinput:
  feedback:
  # dart_rss: ^2.0.1
  # webfeed: ^0.7.0
  xml:
  #    google_mobile_ads:
  #    flutter_image_compress:
  chopper: # ^6.1.1
  camera: ^0.11.0+2 #^0.10.3+2
  camera_android:
  flutter_rating_bar:
  flutter_rating_stars:
  universal_io: #^2.2.0
  flutter_typeahead: #^4.3.7
  # webview_flutter: ^4.10.0  # TODO: Temporarily disabled due to WebKit library issue
  token_parser: #^1.7.0
  flutter_highlight: #^0.7.0
  highlighter: #^0.1.1
  #    open_library_api: ^0.1.6
  html2md: #^1.2.6
  #  flutter_html: #^3.0.0-alpha.6
  # extractor: #^0.0.3
  # google_books_api: #^1.0.1
  # flutter_webrtc: ^0.12.11 #^0.9.24  # TODO: Temporarily disabled due to WebKit library issue
  # flutter_signin_button: #^2.0.0
  google_maps_flutter: #^2.2.5
  # flutter_secure_storage: ^9.2.2
  dart_openai: # ^4.0.0
  sign_in_with_apple: #^4.3.0
  #    the_apple_sign_in: #^1.1.1
  websocket_universal:
  firebase_remote_config: #^4.0.0
  #  permission_handler_web: #^0.0.2
  firebase_auth: ^5.4.0 #^4.5.0
  firebase_auth_web: #^5.4.0
  # flutter_facebook_auth: #^6.0.4  # Disabled - not used
  intl: any
  google_sign_in: #^6.0.0
  cloud_firestore:
  #    webfeed_revised:
  archive:
  # open_filex:
  #    open_file:
  intl_translation: #^0.18.0
  pdf: #^3.8.4
  objectdb: #^1.2.1+1
  go_router: #^6.5.7
  cron: #^0.5.1
  logger: #^1.3.0
  get_it: #^7.2.0
  get_it_mixin: #^4.2.2
  watch_it: #^1.5.1
  image_editor: #^1.3.0
  pro_image_editor: ^9.1.0
  extended_image:
  dio: #^4.0.0
  flutter_spinkit: #^5.1.0
  device_preview:
  # fluttertoast: #^8.0.8 #* not available for mac,
  bot_toast: ^4.1.3
  #    toastification:
  xml2json: #^5.3.1
  flutter_pdfview: #^1.3.0
  # flutter_epub_viewer is depending on inappwebview
  # flutter_epub_viewer:
  chat_gpt_sdk: #^2.0.0
  material_design_icons_flutter: #^6.0.7096
  eva_icons_flutter: #^3.1.0
  firebase_crashlytics: ^4.3.0
  flutter_portal: ^1.1.4
  riverpod: #^2.3.5
  flutter_riverpod:
  hooks_riverpod:
  mobx: #^2.1.4
  #    linkedin_login: #^2.2.1
  flutter_image_slideshow: #^0.1.5
  test_core: #^0.4.20
  web_socket_channel: ^3.0.1 #^2.4.0
  readmore: #^2.2.0
  country_code_picker:
  audioplayers: ^6.1.0
  fl_chart: #^0.62.0
  video_player: #^2.6.1
  intro_slider: #^4.2.0
  local_auth: #^2.1.6
  page_transition:
  # mobile_scanner: ^6.0.0
  in_app_review:
  # in_app_update:
  image_input:
  printing:
  logging: #^1.1.1
  get_storage:
  #  flutter_widget_from_html:
  smooth_page_indicator: #^1.1.0
  flutter_slidable: #^3.0.0
  sliding_up_panel: #^2.0.0+1
  introduction_screen:
  carousel_slider: #^4.2.1
  flutter_form_builder: #^8.0.0
  pull_to_refresh: #^2.0.0
  showcaseview:
  flutter_easyloading:
  vertical_card_pager: #^1.5.0
  get:
  animations: #^2.0.7
  animate_do:
  awesome_notifications: ^0.10.1
  #  ffmpeg_kit_flutter: ^6.0.3
  hive: #^2.2.3
  hive_flutter:
  # isar: *isar_version
  # isar_flutter_libs: *isar_version
  objectbox:
  animated_text_kit: #^4.2.2
  flutter_screenutil: #^5.7.0
  random_avatar: #^0.0.7
  uri:
  pay:
  quick_actions: #^1.0.8
  # stripe_payment:
  flutter_animate:
  # tflite_flutter: #^0.10.1
  #    tflite:
  #    sherpa_onnx:
  #    sherpa_onnx has lots of duplicate symbols with sherpa_onnx and MediaPipeTasksGenAIC
  #    sherpa_onnx:
  onnxruntime:
  # python_ffi:
  # serious_python:
  #  dartpip:
  shimmer: #^3.0.0
  flutter_local_notifications: #^15.1.0+1
  screenshot: #^2.1.0
  #    assets_audio_player: #^3.1.0
  flutter_stripe: #^9.3.0
  # for web:
  flutter_stripe_web:
  in_app_purchase: ^3.2.0
  dart_rss: #^3.0.1
  chatgpt_completions: ^1.0.3
  # google_ml_kit: #^0.16.3
  #    epub_view: #^3.2.0
  # epubx: #^4.0.0
  #    image_editor_plus: ^0.2.6
  # flutter_image_editor: #^2.1.0
  flutter_tts:
  #  flutter_barcode_scanner:
  # qr_code_scanner:
  qr_flutter:
  upgrader:
  app_links:
  speech_to_text:
  flutter_svg:
  material_symbols_icons:
  collection:
  async:
  lottie:
  connectivity_plus:
  flutter_native_splash:
  #  flutter_launcher_icons:
  record:
  mockito:
  firebase_ml_model_downloader: #^0.2.3+7
  flutter_download_manager:
  # background_downloader: ^8.5.6
  download:
  internet_connection_checker:
  excel:
  #    flutter_downloader:
  step_progress_indicator:
  flutter_file_dialog:
  memory_cache:
  flutter_cached_pdfview:
  font_awesome_flutter:
  langchain: #^0.7.5
  langgraph:
  langchain_openai:
  openai_realtime_dart: ^0.0.1+1
  langchain_google:
  #    langchain_huggingface:
  #    langchain_wikipedia:
  langchain_anthropic:
  anthropic_sdk_dart:
  #    langchain_supabase:
  #    langchain_weaviate:
  #    langchain_microsoft:
  langchain_chroma:
  #    langchain_amazon:
  timeago:
  timeline_tile:
  # native_assets_cli: ^0.3.2
  # native_toolchain_c: ^0.3.0
  livekit_client: # ^2.4.0 #^2.3.4
  email_validator:
  responsive_framework:
  grouped_list:
  injectable:
  flutter_staggered_grid_view:
  just_audio:
  badges:
  retrofit:
  retry:
  rxdart:
  infinite_scroll_pagination:
  modal_bottom_sheet:
  curved_navigation_bar:
  easy_localization:
  auto_route:
  table_calendar:
  percent_indicator:
  fluent_ui:
  sensors_plus:
  #    flutter_pytorch:
  #    pytorch_mobile:
  flutter_vision:
  flutter_speed_dial:
  flutter_gemma: 0.2.4
  # path: packages/flutter_gemma
  flutter_gemini: #^2.0.4
  #    firebase_data_connect and firebase_vertexai depends on firebase app check
  firebase_vertexai: #^0.2.2+3
  firebase_data_connect: ^0.1.2+6
  ollama_dart:
  ollama:
  #    llama_cpp_dart:
  #    llama_cpp:
  google_generative_ai:
  firebase_ui_auth:
  firebase_ui_oauth: any
  firebase_ui_oauth_apple: any
  firebase_ui_oauth_facebook: any
  firebase_ui_oauth_google: any
  firebase_ui_oauth_twitter: ^1.3.1
  firebase_ui_localizations:
  firebase_ui_database:
  firebase_ui_shared:
  flutter_chat_ui:
  avatar_glow:
  signature:
  convex_bottom_bar:
  flutter_firebase_chat_core:
  firebase_ui_firestore:
  firebase_ui_storage:
  getwidget:
  flutter_platform_widgets:
  # ffigen:
  ffi:
  flutter_quill: 10.7.7 #^10.8.2
  # flutter_quill_extensions:
  markdown_quill: ^4.1.0
  # appflowy_editor:
  device_info_plus: #^9.1.1 #^11.0.0
  #  system_info_plus: ^0.0.5
  elevenlabs_flutter_updated:
  flutter_code_editor:
  code_editor:
  re_editor:
  super_editor:
  #    visual_editor:
  #        git: https://github.com/visual-space/visual-editor.git
  stomp_dart_client:
  graphite:
  infinite_canvas:
  web_socket_client:
  #        opencv duplicate symbols for ios
  #    opencv_4:
  #    opencv_dart: ^1.2.5
  #        amplify_storage_s3 error: Swift Compiler Error (Xcode): No such module 'AWSMobileClient'
  #    amplify_storage_s3:
  perfect_freehand:
  fluro:

  # lcpp: ^0.1.8
  # sherpa:
  #   path: packages/sherpa
  #  receive_sharing_intent: ^1.8.0
  # receive_sharing_intent: 1.6.7
  system_info2: ^4.0.0
  flutter_resizable_container: ^3.0.0
  lan_scanner:
  langchain_ollama:
  network_info_plus: ^4.1.0+1
  crypto: ^3.0.3
  langchain_mistralai:
  vector_math:
  math_expressions:
  protobuf: ^3.1.0
  protoc_plugin: ^21.1.2
  icons_launcher:
  #    babylon_tts:
  #        path: packages/babylon_tts
  #    elevenlabs_flutter:
  # aub_ai: ^1.0.3
  #    pytorch_lite:
  # whisper_dart:
  #    whisper_flutter: # need to add cpp file
  # whisper_flutter_plus:
  # flutter_play_asset_delivery: #^0.0.2
  #    read_pdf_text:
  #    flutter_file_view: #^2.2.1
  desktop_window: ^0.4.1
  drop_cap_text:
    git:
      url: https://github.com/parlough/drop_cap_text
      ref: feat/adapt-for-deprecations
  extra_alignments: ^1.0.0+1
  flextras: ^1.0.0
  flutter_circular_text: ^0.3.1
  flutter_displaymode: ^0.6.0
  gap: ^3.0.1
  home_widget: ^0.7.0
  image_fade: ^0.6.2
  package_info_plus: ^8.0.3
  particle_field: ^1.0.0
  pointer_interceptor: ^0.10.1+2
  rnd: ^0.2.0
  sized_context: ^1.0.0+4
  unsplash_client: ^2.2.0
  youtube_player_iframe: ^5.2.0
  freezed_annotation:
  freezed:
  flutter_chat_bubble:
  pluto_grid:
  ml_algo:
  ml_linalg:
  flutter_stable_diffusion: #^1.0.0+2
  #    path: packages/flutter_stable_diffusion/flutter_stable_diffusion
  #  flutter_mvc: ^4.1.0
  gal:
  image_painter:
  flutter_painter_v2: ^2.0.1
  painter: ^2.0.0
  pretty_dio_logger:
  flutter_onboarding_slider:
  auto_size_text:
  # rive:
  flutter_neumorphic:
  sliver_tools:
  adaptive_dialog:
  flutter_adaptive_scaffold:
  reactive_forms:
  reactive_forms_annotations:
  # graphql_flutter:
  hydrated_bloc:
  quiver:
  mason:
  cubit_generator: ^1.0.2
  # flame:
  # flame_audio: #^2.10.5
  # A package containing annotations for flutterfire_gen.
  # flutterfire_gen_annotation:

  # A package containing utility annotations for flutterfire_gen.
  # flutterfire_gen_utils:
  flash_card:
  quick_quiz:
  quiz_bank:
  flutter_quiz_matcher:
  quick_quiz_view:
  animated_leaderboard:
  #  fcllama: ^0.0.3
  # rive_common: ^0.4.15
  # sentry_flutter: ^7.20.2  # Temporarily disabled due to iOS build issues
  # stable_diffusion_library_flutter: ^0.0.2
  ggml_library_flutter: ^0.0.10
  whisper_library_flutter: ^0.0.9
  llama_library_flutter: ^0.0.3
  outetts_flutter: ^0.0.5
  flutter_ai_toolkit: ^0.6.8
  supabase_flutter: ^2.9.0
  flutter_gen: ^5.10.0
  llama_sdk: ^0.0.5

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  # drift_dev: ^2.5.2
  build_runner: # ^2.4.6 #^2.3.3
  flutter_driver:
    sdk: flutter
  test: any
  json_serializable: ^6.8.0
  #  convenient_test_dev: ^1.5.0
  ffigen: #^9.0.1
  # flutterfire_gen:
  # desktop_drop:

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: #^2.0.0
  dartpip: ^0.2.8
  # isar_generator: *isar_version #^3.1.0+1
  # openapi_generator: ^1.1.4
  # swagger
  chopper_generator: #^4.0.5
  swagger_dart_code_generator: #^2.4.6
  retrofit_generator:
  injectable_generator:
  reactive_forms_generator:
  melos: ^6.2.0
  mirai:
  flutter_widget_from_html:
  # flutter_ume: ^1.0.1
  # flutter_ume_kit_ui: ^1.0.0
  # flutter_ume_kit_device: ^1.0.0
  # flutter_ume_kit_perf: ^1.0.0
  # flutter_ume_kit_show_code: ^1.0.0
  # flutter_ume_kit_console: ^1.0.0
  # flutter_ume_kit_dio: ^1.0.0
  flutter_launcher_icons: "^0.14.2"
  msix: ^3.16.8
  flutter_gen_runner:

msix_config:
  display_name: Diogenes AI Chatbot
  publisher_display_name: Diogenes Consulting LLC
  identity_name: 33484dsx1986.DiogenesAIChatbot
  msix_version: 1.0.906.0
  logo_path: C:\Users\<USER>\Documents\git\diogenesaichatbot\assets\images\app_icon.png
  capabilities: internetClient, location, microphone, webcam
  store: true
  sign_msix: false
  publisher: CN=3D8A7DBA-0958-40D6-963E-7BF82A65E0D5

#
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/chat_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/chat_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/images/chat_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/chat_icon.png"

dependency_overrides:
  intl: ^0.19.0

ffigen:
  name: "diogenes_cpp"
  output: lib/generated_bindings_llama.dart
  headers:
    entry-points:
      - "src/llama.cpp/include/llama.h"
      - "src/llama.cpp/ggml/include/ggml.h"
      - "src/llama.cpp/utils.h"
    include-directives:
      - "src/llama.cpp/include/llama.h"
      - "src/llama.cpp/ggml/include/ggml.h"
      - "src/llama.cpp/utils.h"
    #   - "src/llama.cpp/ggml/include/"
    #   - "src/llama.cpp/include/"
    #   - "src/llama.cpp/common/*"
    #   - "src/llama.cpp/*"

  # compiler-opts:
  #   - "-I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.0.sdk/usr/include/c++/v1"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env.development
    - .env.production
    - assets/images/
    - assets/sounds/
    - assets/fonts/
    - assets/huggingface_models.json
    - assets/labels/
    - assets/flutter_vision/
    - assets/
    - assets/page_icons/
    - assets/libs/

  fonts:
    - family: Cinzel
      fonts:
        - asset: assets/fonts/CinzelDecorative-Regular.ttf
        - asset: assets/fonts/CinzelDecorative-Black.ttf
        - asset: assets/fonts/CinzelDecorative-Bold.ttf
    - family: Yeseva
      fonts:
        - asset: assets/fonts/YesevaOne-Regular.ttf
    - family: Tenor
      fonts:
        - asset: assets/fonts/TenorSans-Regular.ttf
    - family: Raleway
      fonts:
        - asset: assets/fonts/Raleway-Regular.ttf
        - asset: assets/fonts/Raleway-Italic.ttf
          style: italic
        - asset: assets/fonts/Raleway-Medium.ttf
          weight: 500
        - asset: assets/fonts/Raleway-Bold.ttf
          weight: 700
        - asset: assets/fonts/Raleway-ExtraBold.ttf
          weight: 800
    - family: MaShanZheng
      fonts:
        - asset: assets/fonts/MaShanZheng-Regular.ttf
    - family: B612Mono
      fonts:
        - asset: assets/fonts/B612Mono-Regular.ttf

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_native_splash:
  # This package generates native code to customize Flutter's default white native splash screen
  # with background color and splash image.
  # Customize the parameters below, and run the following command in the terminal:
  # dart run flutter_native_splash:create
  # To restore Flutter's default white splash screen, run the following command in the terminal:
  # dart run flutter_native_splash:remove

  # IMPORTANT NOTE: These parameter do not affect the configuration of Android 12 and later, which
  # handle splash screens differently that prior versions of Android.  Android 12 and later must be
  # configured specifically in the android_12 section below.

  # color or background_image is the only required parameter.  Use color to set the background
  # of your splash screen to a solid color.  Use background_image to set the background of your
  # splash screen to a png image.  This is useful for gradients. The image will be stretch to the
  # size of the app. Only one parameter can be used, color and background_image cannot both be set.
  color: "#42a5f5"
  # background_image: "assets/background.png"

  # Optional parameters are listed below.  To enable a parameter, uncomment the line by removing
  # the leading # character.

  # The image parameter allows you to specify an image used in the splash screen.  It must be a
  # png file and should be sized for 4x pixel density.
  image: assets/splash_mobile.png

  # The branding property allows you to specify an image used as branding in the splash screen.
  # It must be a png file. It is supported for Android, iOS and the Web.  For Android 12,
  # see the Android 12 section below.
  #branding: assets/dart.png

  # To position the branding image at the bottom of the screen you can use bottom, bottomRight,
  # and bottomLeft. The default values is bottom if not specified or specified something else.
  #branding_mode: bottom

  # Set the branding padding from the bottom of the screen.  The default value is 0
  # branding_bottom_padding: 24

  # The color_dark, background_image_dark, image_dark, branding_dark are parameters that set the background
  # and image when the device is in dark mode. If they are not specified, the app will use the
  # parameters from above. If the image_dark parameter is specified, color_dark or
  # background_image_dark must be specified.  color_dark and background_image_dark cannot both be
  # set.
  #color_dark: "#042a49"
  #background_image_dark: "assets/dark-background.png"
  #image_dark: assets/splash-invert.png
  #branding_dark: assets/dart_dark.png

  # From Android 12 onwards, the splash screen is handled differently than in previous versions.
  # Please visit https://developer.android.com/guide/topics/ui/splash-screen
  # Following are specific parameters for Android 12+.
  android_12:
  # The image parameter sets the splash screen icon image.  If this parameter is not specified,
  # the app's launcher icon will be used instead.
  # Please note that the splash screen will be clipped to a circle on the center of the screen.
  # App icon with an icon background: This should be 960×960 pixels, and fit within a circle
  # 640 pixels in diameter.
  # App icon without an icon background: This should be 1152×1152 pixels, and fit within a circle
  # 768 pixels in diameter.
  #image: assets/android12splash.png

  # Splash screen background color.
  #color: "#42a5f5"

  # App icon background color.
  #icon_background_color: "#111111"

  # The branding property allows you to specify an image used as branding in the splash screen.
  #branding: assets/dart.png

  # The image_dark, color_dark, icon_background_color_dark, and branding_dark set values that
  # apply when the device is in dark mode. If they are not specified, the app will use the
  # parameters from above.
  #image_dark: assets/android12splash-invert.png
  #color_dark: "#042a49"
  #icon_background_color_dark: "#eeeeee"

  # The android, ios and web parameters can be used to disable generating a splash screen on a given
  # platform.
  #android: false
  #ios: false
  #web: false

  # Platform specific images can be specified with the following parameters, which will override
  # the respective parameter.  You may specify all, selected, or none of these parameters:
  #color_android: "#42a5f5"
  #color_dark_android: "#042a49"
  #color_ios: "#42a5f5"
  #color_dark_ios: "#042a49"
  #color_web: "#42a5f5"
  #color_dark_web: "#042a49"
  #image_android: assets/splash-android.png
  #image_dark_android: assets/splash-invert-android.png
  #image_ios: assets/splash-ios.png
  #image_dark_ios: assets/splash-invert-ios.png
  #image_web: assets/splash-web.gif
  #image_dark_web: assets/splash-invert-web.gif
  #background_image_android: "assets/background-android.png"
  #background_image_dark_android: "assets/dark-background-android.png"
  #background_image_ios: "assets/background-ios.png"
  #background_image_dark_ios: "assets/dark-background-ios.png"
  #background_image_web: "assets/background-web.png"
  #background_image_dark_web: "assets/dark-background-web.png"
  #branding_android: assets/brand-android.png
  #branding_bottom_padding_android: 24
  #branding_dark_android: assets/dart_dark-android.png
  #branding_ios: assets/brand-ios.png
  #branding_bottom_padding_ios: 24
  #branding_dark_ios: assets/dart_dark-ios.png
  #branding_web: assets/brand-web.gif
  #branding_dark_web: assets/dart_dark-web.gif

  # The position of the splash image can be set with android_gravity, ios_content_mode, and
  # web_image_mode parameters.  All default to center.
  #
  # android_gravity can be one of the following Android Gravity (see
  # https://developer.android.com/reference/android/view/Gravity): bottom, center,
  # center_horizontal, center_vertical, clip_horizontal, clip_vertical, end, fill, fill_horizontal,
  # fill_vertical, left, right, start, or top.
  android_gravity: center
  #
  # ios_content_mode can be one of the following iOS UIView.ContentMode (see
  # https://developer.apple.com/documentation/uikit/uiview/contentmode): scaleToFill,
  # scaleAspectFit, scaleAspectFill, center, top, bottom, left, right, topLeft, topRight,
  # bottomLeft, or bottomRight.
  #ios_content_mode: center
  #
  # web_image_mode can be one of the following modes: center, contain, stretch, and cover.
  #web_image_mode: center

  # The screen orientation can be set in Android with the android_screen_orientation parameter.
  # Valid parameters can be found here:
  # https://developer.android.com/guide/topics/manifest/activity-element#screen
  #android_screen_orientation: sensorLandscape

  # To hide the notification bar, use the fullscreen parameter.  Has no effect in web since web
  # has no notification bar.  Defaults to false.
  # NOTE: Unlike Android, iOS will not automatically show the notification bar when the app loads.
  #       To show the notification bar, add the following code to your Flutter app:
  #       WidgetsFlutterBinding.ensureInitialized();
  #       SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.bottom, SystemUiOverlay.top], );
  #fullscreen: true

  # If you have changed the name(s) of your info.plist file(s), you can specify the filename(s)
  # with the info_plist_files parameter.  Remove only the # characters in the three lines below,
  # do not remove any spaces:
  #info_plist_files:
  #  - 'ios/Runner/Info-Debug.plist'
  #  - 'ios/Runner/Info-Release.plist'
#flutter_intl:
#  enabled: true
